#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import argparse
import os
import jinja2
import yaml
import logging
import traceback
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%Y-%m-%dT%H:%M:%S.%fZ'
)

def flatten_dict(d, parent_key='', sep='_'):
    """将嵌套字典扁平化"""
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)

def process_jinja2_placeholders(config, variables):
    """处理配置中的 Jinja2 模板占位符，使用变量文件中的值进行替换"""
    try:
        # 将配置转换为字符串
        config_str = json.dumps(config)
        logging.info(f"原始配置字符串长度: {len(config_str)}")
        
        # 创建模板并设置变量
        template = jinja2.Template(config_str)
        
        try:
            # 尝试渲染模板
            rendered_config = template.render(**variables)
            logging.info(f"渲染后的配置字符串长度: {len(rendered_config)}")
            
            # 尝试解析 JSON
            try:
                return json.loads(rendered_config)
            except json.JSONDecodeError as e:
                # 记录错误位置附近的上下文
                error_pos = e.pos
                context_start = max(0, error_pos - 50)
                context_end = min(len(rendered_config), error_pos + 50)
                error_context = rendered_config[context_start:context_end]
                logging.error(f"JSON 解析错误: {str(e)}")
                logging.error(f"错误位置附近的上下文: {error_context}")
                raise
                
        except jinja2.UndefinedError as e:
            # 如果渲染失败，获取所有缺失的变量
            required_vars = set()
            for match in template.environment.parse(config_str).find_all(jinja2.nodes.Name):
                required_vars.add(match.name)
            
            missing_vars = required_vars - set(variables.keys())
            if missing_vars:
                error_msg = f"缺少必需的变量: {', '.join(missing_vars)}"
                logging.error(error_msg)
                raise ValueError(error_msg)
            else:
                logging.error(f"模板渲染错误: {str(e)}")
                raise
                
    except Exception as e:
        logging.error(f"处理 Jinja2 占位符时发生错误: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise

def load_variables(file_path):
    """根据文件扩展名加载变量文件"""
    try:
        if not file_path or not os.path.exists(file_path):
            logging.warning(f"变量文件不存在: {file_path}")
            return {}
            
        ext = os.path.splitext(file_path)[1].lower()
        with open(file_path, 'r') as f:
            if ext in ['.yaml', '.yml']:
                variables = yaml.safe_load(f)
                logging.info(f"成功加载 YAML 变量文件: {file_path}")
                return variables
            elif ext == '.json':
                variables = json.load(f)
                logging.info(f"成功加载 JSON 变量文件: {file_path}")
                return variables
            else:
                error_msg = f"不支持的变量文件格式: {ext}"
                logging.error(error_msg)
                raise ValueError(error_msg)
    except Exception as e:
        logging.error(f"加载变量文件时发生错误: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise

def update_config(dst_path, out_path, variables_path, src_path=None):
    try:
        # 读取目标配置（需要更新的文件）
        with open(dst_path, 'r') as f:
            dst_config = json.load(f)
        logging.info(f"成功读取目标配置文件: {dst_path}")
        
        # 如果有源配置，则进行合并
        if src_path and os.path.exists(src_path):
            # 读取源配置（新的值）
            with open(src_path, 'r') as f:
                src_config = json.load(f)
            logging.info(f"成功读取源配置文件: {src_path}")
            
            # 递归更新配置（用源配置中的值更新目标配置）
            def deep_update(target, updates):
                for key, value in updates.items():
                    if isinstance(value, dict) and key in target and isinstance(target[key], dict):
                        deep_update(target[key], value)
                    else:
                        target[key] = value
                return target
            
            # 执行更新
            updated = deep_update(dst_config, src_config)
            logging.info("成功合并源配置和目标配置")
        else:
            updated = dst_config
            logging.info("未提供源配置文件，使用目标配置")
        
        # 读取变量文件
        variables = load_variables(variables_path)
        
        # 处理 Jinja2 模板占位符
        final_config = process_jinja2_placeholders(updated, variables) if variables else updated
        
        # 生成更新后的配置文件
        with open(out_path, 'w', encoding='utf-8') as f:
            json.dump(final_config, f, indent=2, ensure_ascii=False)
        logging.info(f"成功生成更新后的配置文件: {out_path}")
        
    except Exception as e:
        logging.error(f"更新配置时发生错误: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    try:
        parser = argparse.ArgumentParser(description='Update environment configurations')
        parser.add_argument('--src', help='Source config file path (containing new values)')
        parser.add_argument('--dst', required=True, help='Destination config file path (file to be updated)')
        parser.add_argument('--out', required=True, help='Output file path')
        parser.add_argument('--variables', help='Variables file path (YAML or JSON)')
        args = parser.parse_args()
        
        logging.info("开始配置更新流程")
        logging.info(f"源文件: {args.src}")
        logging.info(f"目标文件: {args.dst}")
        logging.info(f"输出文件: {args.out}")
        logging.info(f"变量文件: {args.variables}")
        
        update_config(args.dst, args.out, args.variables, args.src)
        logging.info("✅ 配置更新完成")
        
    except Exception as e:
        logging.error(f"程序执行失败: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        exit(1)