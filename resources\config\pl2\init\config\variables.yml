# 数据库配置
global_database_used_db_name: "nsds0"
global_database_used_db_type: "oracle"
global_database_used_db_user: "BANGCLE_EVERISK_V4_512"
global_database_pg_password: "imPwPRP4W/DGcZEMBYnkEw=="
postgres_port: 11521

# Kafka配置
global_kafka_everisk_kafka_kerberos_user: "bangcle_user"
kafka_port: 21007

# Elasticsearch配置
global_elasticsearch_everisk_elasticsearch_cluster_name: "elasticsearch_cluster"
elasticsearch_client_tcp_port: 24101
elasticsearch_client_restful_port: 24100
elasticsearch_shard_number: 16

# Kibana配置
kibana_port: 5601

# Redis配置
redis_password: ""
redis_port: 22401

# HBase配置
global_hbase_everisk_hbase_kerberos_user: "bangcle_user"

# HDFS配置
global_hdfs_everisk_hdfs_kerberos_user: "bangcle_user"

# Nebula配置
global_nebula_everisk_default_nebula_space: "bangcle"
global_nebula_everisk_nebula_password: "X0wHoFZPKw21wZlV5+S5gA=="
global_nebula_everisk_nebula_user: "bangcle_user"

# Zookeeper配置
zookeeper_port: 24002

# 路径配置
transfer_transfer_data_local_path: "/home/<USER>/sds/RKDT/transfer/data/"
transfer_transfer_data_hdfs_path: "/home/<USER>/sds/RKDT/data/"
alarmservice_alarmservice_email_file_path: "/home/<USER>/sds/data/web-service/data"
webservice_system_info_file_path: "/home/<USER>/sds/RKDT/web-service/report/"
webservice_webservice_report_dir: "/home/<USER>/sds/RKDT/web-service/report/"
webservice_webservice_data_local_path: "/home/<USER>/sds/RKDT/web-service/report/"
everisk_config_zk_namespace_node: "/bangcle_config/v4.6.0.2/dti"
ccb_logstash_config_file_path: "/home/<USER>/sds/RKDT/logstash-8.7.1"

# 服务配置
webservice_server_port: 9999
business_url: "http://***************:6279"
webservice_iot_passwd: ""
webservice_free_password_user: "sds"
webservice_webservice_cache_ttl_seconds: 3600
webservice_ccb_uass_auth_key: "3030a3d03f884ef79f9a1ad8810de91f"
webservice_ccb_uass_servers_url: "http://**********:9016/uass-core/serveraddress.do"
webservice_ccb_api_platform_host: "http://************:8125"
push_service_core_push_ccb_push_post_server: "http://**************:9443"
nginx_port: 6279
everisk_upgrade_service_port: 5799
webservice_upgrade_plugin_dir: "/home/<USER>/sds/RKDT/web-service/data/client/plugin/"
webservice_server_servlet_session_timeout: "3650D"

# Minio配置
minio_user: "bangcle"
minio_password: "beap1234"
minio_port: 9000

# 用户配置
bangcle_user: "ap"

# 功能开关
esDataAggregater_index_close_switch: false
switch_main_function_banned_enabled: false

# 性能配置
event_data_preparation_business_number_of_threads: 4

# 服务器组配置
groups:
  postgres:
    - "*************"
  kafka:
    - "***************"
    - "***************"
    - "***************"
  elasticsearchClient:
    - "***************"
  kibana:
    - "***************"
  redis:
    - "***************"
  zookeeper:
    - "***************"
    - "***************"
    - "***************"
  nebula:
    - "***************"
    - "***************"
    - "***************"
  crash:
    - "***************"
  web-service:
    - "128.196.118.137"
  minio:
    - "***************"
  nginx:
    - "***************"
  upgrade-service:
    - "***************" 