# Everisk 环境配置文件
# 此文件包含所有环境相关的配置，包括环境参数、凭据和构建设置

# 环境配置部分 - 定义不同环境的连接参数
environments:
  # PL2环境 - 开发测试环境
  pl2:
    name: PL2
    host: ***************  # 主机地址
    port: 8181             # 端口号
    description: "开发测试环境"
    hadoop_source_dir: "/home/<USER>/sds/RKDT/config"    # Hadoop配置文件源目录（可选，默认为/home/<USER>/sds/RKDT/config）
    kerberos_source_dir: "/home/<USER>/sds/RKDT/config"  # Kerberos配置文件源目录（可选，默认为/home/<USER>/sds/RKDT/config）
    config_files:          # 需要处理的配置文件列表
      - src_path: "pl2/init/config/everisk.json.j2" # 源文件路径(相对于共享库资源的resources/config目录)
        dst_path: "init/config/everisk.json" # 目标文件路径(相对于检出代码的package目录)
        out_path: "pl2/init/config/everisk.json" # 替换后输出文件路径(相对于检出代码的package/Scripts目录)
        variables_file: "pl2/init/config/variables.yml" # 变量文件路径

  # VT环境 - 验证测试环境
  vt:
    name: VT
    host: ***************
    port: 8181
    description: "验证测试环境"
    hadoop_source_dir: "/home/<USER>/sds/RKDT/config"    # Hadoop配置文件源目录（可选，默认为/home/<USER>/sds/RKDT/config）
    kerberos_source_dir: "/home/<USER>/sds/RKDT/config"  # Kerberos配置文件源目录（可选，默认为/home/<USER>/sds/RKDT/config）
    config_files:          # 需要处理的配置文件列表
      - src_path: "pl2/init/config/everisk.json.j2"
        dst_path: "init/config/everisk.json"
        out_path: "vt/init/config/everisk.json"
        variables_file: "pl2/init/config/variables.yml"

  # PROD环境 - 生产环境
  prod:
    name: PROD
    host: ***********
    port: 8181
    description: "生产环境"
    config_files:          # 需要处理的配置文件列表
      - src_path: "prod/init/config/everisk.json.j2"
        dst_path: "init/config/everisk.json"
        out_path: "prod/init/config/everisk.json"
        variables_file: "prod/init/config/variables.yml"

# 凭据配置部分 - 定义各种服务的访问凭据
credentials:
  # Git凭据配置
  git:
    id: junguang.chen        # Jenkins中配置的凭据ID
    username_var: GIT_USERNAME  # 用户名环境变量名
    password_var: GIT_PASSWORD  # 密码环境变量名
    
    # Git Parameter插件配置 - 控制分支和标签选择的行为
    parameter:
      type: PT_BRANCH_TAG      # 参数类型：PT_BRANCH_TAG(分支和标签)、PT_BRANCH(仅分支)、PT_TAG(仅标签)
      branch_filter: "origin/(.*)" # 分支过滤器，使用正则表达式
      tag_filter: '*'          # 标签过滤器，使用通配符
      sort_mode: DESCENDING_SMART # 排序方式：ASCENDING_SMART(升序)、DESCENDING_SMART(降序)、NONE(不排序)
      list_size: '10'          # 下拉列表显示的条目数量（必须是字符串类型）
  
  # 企业微信机器人配置
  wxwork:
    robot: robot1              # 企业微信机器人ID

# 构建配置部分 - 定义构建相关的参数
build:
  # 默认构建模块列表
  modules: "init,cleaner,threat,transfer,analyzer-dev,receiver,threat-index,web-service,data-model-statistics,ccb-transfer-pbc"
  
  # 可选的构建模式
  modes:
    - ccb
    - main
    - hxb
    - cebbank