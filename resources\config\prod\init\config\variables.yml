# 数据库配置
global_database_used_db_name: "SDSDBPD11"
global_database_used_db_type: "oracle"
global_database_used_db_user: "SDSJC"
global_database_pg_password: "f1qvSL6H0pth8tvNcWM+gA=="
postgres_port: 11521
# Kafka配置
global_kafka_everisk_kafka_kerberos_user: "bangcle_user"
global_kafka_everisk_kafka_brokers: "************:21007,************:21007,************:21007,************:21007,************:21007"

# Elasticsearch配置
global_elasticsearch_everisk_elasticsearch_cluster_name: "elasticsearch_cluster"
global_elasticsearch_everisk_elasticsearch_host_names: "*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,************,************,************,************,************,************,************,************,************,************,************"
elasticsearch_client_tcp_port: "24107"
elasticsearch_client_restful_port: "24106"
elasticsearch_shard_number: "16"
global_elasticsearch_everisk_elasticsearch_shard_size: "32212254720"
global_elasticsearch_everisk_elasticsearch_login: "elastic:beap123"

# Kibana配置
global_kibana_everisk_kibana_host_name: "http://***********"
kibana_port: "5602"

# Redis配置
redis_active_mode: "cluster"
redis_host: "************"
redis_port: "22401"
redis_password: ""

# HBase配置
global_hbase_everisk_hbase_kerberos_user: "bangcle_user"
global_hbase_everisk_hbase_stats_interval: "60"

# Phoenix配置
global_phoenix_everisk_phoenix_url: "*************************,************,************:24002"

# Zookeeper配置
zookeeper_server_list: "************:24002,************:24002,************:24002"
zookeeper_port: 24002
# HDFS配置
global_hdfs_everisk_hdfs_kerberos_user: "bangcle_user"

# Nebula配置
global_nebula_everisk_default_nebula_space: "bangcle"
global_nebula_everisk_nebula_host: "***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699,***********:3699"
global_nebula_everisk_nebula_password: "X0wHoFZPKw21wZlV5+S5gA=="

# kafka配置
kafka_port: 21007

# 路径配置 
transfer_transfer_data_local_path: "/home/<USER>/sds/RKDT/transfer/data/"
transfer_transfer_data_hdfs_path: "/home/<USER>/sds/RKDT/data/"
alarmservice_alarmservice_email_file_path: "/home/<USER>/sds/data/web-service/data"
webservice_system_info_file_path: "/home/<USER>/sds/RKDT/web-service/report/"
webservice_webservice_report_dir: "/home/<USER>/sds/RKDT/web-service/report/"
webservice_upgrade_plugin_dir: "/home/<USER>/sds/RKDT/web-service/data/client/plugin/"
webservice_webservice_data_local_path: "/home/<USER>/sds/RKDT/web-service/report/"
everisk_config_zk_namespace_node: "/bangcle_config/v4.6.0.2/dti"

# 服务配置
webservice_server_port: "9999"
business_url: "http://***********:6279"
webservice_iot_passwd: ""
webservice_free_password_user: "sds"
webservice_webservice_cache_ttl_seconds: "3600"
webservice_ccb_uass_auth_key: "a1807c1f19f44b288185a5593e9a4605"
webservice_ccb_uass_servers_url: "http://***********:9116/uass-core/serveraddress.do, http://************:9116/uass-core/serveraddress.do"
webservice_ccb_api_platform_host: "http://************:8125"
push_service_core_push_ccb_push_post_server: "http://************:8070"
nginx_port: "6279"
everisk_upgrade_service_port: "5799"
ftp_server_login_password: "beap1qaz@WSX"
global_kibana_everisk_kibana_login: {"username": "elastic", "password": "beap1qaz@WSX"}
global_nebula_everisk_nebula_user: "bangcle_user"
switch_main_function_banned_enabled: false
ccb_logstash_config_file_path: "/home/<USER>/sds/RKDT/logstash-8.7.1"
event_data_preparation_business_number_of_threads: 8
webservice_server_servlet_session_timeout: "3650D" 


# Minio配置
minio_user: "bangcle"
minio_password: "beap1234"
minio_port: "9000"

# 用户配置
bangcle_user: "ap"

# 服务器组配置
groups:
  postgres:
    - "************"
  kafka:
    - "************"
    - "************"
    - "************"
    - "************"
    - "************"
  elasticsearchClient:
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "*************"
    - "************"
    - "************"
    - "************"
    - "************"
    - "************"
    - "************"
    - "************"
    - "************"
    - "************"
    - "************"
    - "************"
  kibana:
    - "***********"
  redis:
    - "************"
  zookeeper:
    - "************"
    - "************"
    - "************"
  nebula:
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
  crash:
    - "***********"
  web-service:
    - "***********"
  minio:
    - "***********"
  nginx:
    - "***********"
  upgrade-service:
    - "***********" 
