package org.everisk.service

class ConfigMergeService {
    private def script
    private def configService
    
    ConfigMergeService(script) {
        this.script = script
        this.configService = new ConfigurationService(script)
    }
    
    def mergeAllEnvironments(projectName) {
        validatePythonEnvironment()
        
        // 目录检查保持原样
        def initDir = "${projectName}/package/init"
        if (!script.fileExists(initDir)) {
            script.echo "跳过配置合并：${initDir} 目录不存在"
            return
        }
        
        // 统一路径处理
        def workspacePath = script.env.WORKSPACE
        def tempDir = "${workspacePath}/temp_configs"
        def scriptPath = preparePythonScript()
        
        // 创建所有必要的目录
        def scriptsDir = "${workspacePath}/${projectName}/package/Scripts"
        script.sh "mkdir -p '${tempDir}' '${scriptsDir}'"
        
        try {
            configService.getAllEnvironments().each { envName, envConfig ->
                // 获取环境配置
                def configFiles = envConfig.config_files
                
                if (!configFiles) {
                    script.echo "⚠️ 警告：环境 ${envName} 没有定义 config_files"
                    return
                }
                
                script.echo "🔍 处理环境 ${envName} 的配置文件"
                
                // 处理每个配置文件
                configFiles.each { configFile ->
                    def srcPath = configFile.src_path
                    def dstPath = configFile.dst_path
                    def outPath = configFile.out_path
                    
                    if (!dstPath || !outPath) {
                        script.echo "⚠️ 警告：配置文件定义不完整，缺少必需参数(dst_path或out_path)，跳过处理"
                        return
                    }
                    
                    // 如果有源配置，检查源配置文件是否存在
                    if (srcPath) {
                        def sourceConfigPath = "config/${srcPath}"
                        try {
                            script.libraryResource(sourceConfigPath)
                        } catch (Exception e) {
                            script.echo "⚠️ 警告：共享库中配置文件不存在: ${sourceConfigPath}"
                            return
                        }
                    }
                    
                    script.echo "📋 开始处理配置文件${srcPath ? '合并' : '变量替换'}"
                    
                    // 处理配置文件合并
                    processJsonFile(srcPath, dstPath, outPath, envName, projectName, tempDir, scriptPath, configFile)
                }
                
                script.echo "✅ 已完成 ${envName} 环境的配置合并"
            }
        } finally {
            // cleanResources(scriptPath, tempDir)
            script.echo "✅ 已完成所有环境的配置合并"
        }
    }
    
    // 处理单个 JSON 文件
    private def processJsonFile(String srcPath, String dstPath, String outPath,
                          String envName, String projectName, 
                          String tempDir, String mergeScriptPath, def configFile) {
        try {
            // 源文件完整路径（共享库资源中）
            def sourceConfigPath = srcPath ? "config/${srcPath}" : null
            
            // 临时文件路径
            def sourceTempPath = srcPath ? "${tempDir}/${envName}_source_${srcPath.replaceAll('/', '_')}" : null
            
            // 目标文件路径（检出目录下的 package 目录）
            def targetFilePath = "${script.env.WORKSPACE}/${projectName}/package/${dstPath}"
            
            // 输出文件完整路径
            def outputFilePath = "${script.env.WORKSPACE}/${projectName}/package/Scripts/${outPath}"
            
            // 变量文件路径
            def variablesFilePath = configFile?.variables_file ? "${tempDir}/${envName}_variables_${configFile.variables_file.replaceAll('/', '_')}" : null
            
            script.echo "📄 处理配置文件${srcPath ? '合并' : '变量替换'}: ${targetFilePath}"
        
            // 如果有源配置文件，则读取并写入临时文件
            if (srcPath) {
                def sourceContent = script.libraryResource(sourceConfigPath)
                
                if (!sourceContent) {
                    script.echo "⚠️ 警告：配置文件内容为空"
                    return
                }
                
                // 写入临时文件
                script.writeFile(file: sourceTempPath, text: sourceContent)
                
                // 验证临时文件是否创建成功
                def sourceExists = script.sh(
                    script: "test -f '${sourceTempPath}' && echo 'true' || echo 'false'",
                    returnStdout: true
                ).trim()
                
                if (sourceExists != 'true') {
                    script.echo "⚠️ 警告：临时配置文件创建失败"
                    return
                }
            }
            
            // 确保输出目录存在
            def outputDir = new File(outputFilePath).parent
            script.sh "mkdir -p '${outputDir}'"
            
            // 使用 merge_configs.py 合并配置文件
            script.echo "🔄 正在${srcPath ? '合并' : '处理'}配置文件"
            
            // 准备变量文件
            if (configFile?.variables_file) {
                try {
                    // 读取变量文件内容并写入临时文件
                    def variablesContent = script.libraryResource("config/${configFile.variables_file}")
                    script.writeFile(file: variablesFilePath, text: variablesContent)
                } catch (Exception e) {
                    script.echo "⚠️ 变量文件不存在或无法读取: ${configFile.variables_file}, 跳过变量替换"
                    variablesFilePath = null
                }
            }
            
            // 构建命令
            def command = "python3 '${mergeScriptPath}'"
            
            if (srcPath) {
                command += " --src '${sourceTempPath}'"
            }
            
            command += " --dst '${targetFilePath}' --out '${outputFilePath}'"
            
            if (variablesFilePath) {
                command += " --variables '${variablesFilePath}'"
            }
            
            script.sh command
            
            script.echo "✅ 已${srcPath ? '合并' : '处理'}配置文件: ${outputFilePath}.${envName}"
        } catch (Exception e) {
            script.echo "⚠️ 处理 JSON 文件时发生错误: ${e.message}"
        }
    }
    
    // 准备Python脚本
    private def preparePythonScript() {
        def scriptPath = "${script.env.WORKSPACE}/merge_configs.py"
        script.writeFile(
            file: scriptPath,
            text: script.libraryResource('scripts/merge_configs.py')
        )
        script.sh "chmod +x '${scriptPath}'"
        return scriptPath
    }
    
    // 清理资源方法
    private def cleanResources(scriptPath, tempDir) {
        script.sh """
            rm -f '${scriptPath}' 
            rm -rf '${tempDir}'
        """
    }
    
    private def validatePythonEnvironment() {
        def pyCheck = script.sh(
            script: 'python3 --version || echo "PYTHON_MISSING"',
            returnStdout: true
        ).trim()
        
        if (pyCheck.contains("PYTHON_MISSING")) {
            script.error "Python3 runtime missing"
        } else if (!(pyCheck =~ /Python 3\.\d+/)) {
            script.error "Require Python3, current: ${pyCheck}"
        }
    }
}