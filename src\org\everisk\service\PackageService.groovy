package org.everisk.service

class PackageService implements Serializable {
    def script

    PackageService(script) {
        this.script = script
    }

    def createPackage(Map config) {
        // 始终使用参数传入的分支名称
        def branchToUse = config.branch
        
        def sanitizedBranch = branchToUse.replaceAll(/[\/\\]/, '_')
        def timestamp = script.sh(script: 'date +%Y%m%d%H%M%S', returnStdout: true).trim()
        def packageName = "${config.projectName}_${sanitizedBranch}_${timestamp}.tar.gz"
        def packagePath = "${script.WORKSPACE}/${packageName}"
        def md5FilePath = "${packagePath}.md5"

        script.dir(config.projectName) {

            script.sh """
                #!/bin/bash
                set -eo pipefail

                # 检查package目录是否存在及是否为空
                if [ ! -d "package" ]; then
                    echo "❌ 错误：package目录不存在于 ${script.PWD}"
                    exit 1
                fi

                if [ -z "\$(ls -A package)" ]; then
                    echo "⚠️ 警告：package目录为空，无内容可打包"
                    exit 1
                fi

                # 创建压缩包（保留子目录结构）
                PROJECT_NAME='${config.projectName}'
                echo "🚀 正在生成压缩包: ${packageName}"
                tar -czf "${packagePath}" --transform "s,^,\${PROJECT_NAME}/," -C package .

                # 生成校验文件
                md5sum "${packagePath}" > "${md5FilePath}"
            """
        }
        
        // 归档制品
        script.archiveArtifacts(
            artifacts: packageName,
            allowEmptyArchive: false,
            onlyIfSuccessful: true
        )
    }
}