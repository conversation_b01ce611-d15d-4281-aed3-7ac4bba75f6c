package org.everisk.service

class NotificationService implements Serializable {
    def script

    NotificationService(script) {
        this.script = script
    }

    def sendSuccess(Map config) {
        script.wxwork(
            robot: 'robot1',
            type: 'text',
            text: [
                "构建成功 ✅",
                "📦 制品地址：${config.artifactsUrl}",
                "🔍 构建详情：${config.buildUrl}"
            ]
        )
    }

    def sendFailure(Map config) {
        def failureReason = script.currentBuild.getBuildCauses().toString()
        if(config.failureStage) {
            failureReason += "（失败阶段：${config.failureStage}）"
        }
        
        script.wxwork(
            robot: 'robot1',
            type: 'text',
            text: [
                "构建失败 ❌",
                "⛔ 失败原因：${failureReason}",
                "📄 日志查看：${config.buildUrl}console",
                "⚙️ 重试构建：${config.buildUrl}rebuild"
            ]
        )
    }
} 