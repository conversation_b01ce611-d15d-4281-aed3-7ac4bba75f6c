package org.everisk.ccb.service

import org.everisk.service.ConfigurationService

class ConfigService implements Serializable {
    def script
    def configurationService
    private static final String SCRIPTS_DIR = "package/Scripts"
    private static final String CONFIG_DIR = "config"
    private static final String INIT_MODULE = "init"

    ConfigService(script) {
        this.script = script
        this.configurationService = new ConfigurationService(script)
    }

    def generateEnvironmentScripts(Map config) {
        if (!config?.projectName) {
            script.error "项目名称不能为空"
            return
        }

        try {
            script.dir(config.projectName) {
                createScriptsDirectory()
                generateEnvironmentSpecificScripts()
                setScriptPermissions()
                script.echo "✅ 环境配置脚本生成完成"
            }
        } catch (Exception e) {
            script.error "生成环境脚本时发生错误: ${e.message}"
        }
    }

    private def createScriptsDirectory() {
        script.sh "mkdir -p ${SCRIPTS_DIR}"
    }

    private def generateEnvironmentSpecificScripts() {
        def environments = configurationService.getAllEnvironments()
        if (!environments) {
            script.error "没有找到任何环境配置"
            return
        }

        environments.each { env, envConfig ->
            if (!validateEnvironmentConfig(env, envConfig)) {
                script.echo "⚠️ 跳过环境 ${env} 的配置生成，配置信息不完整"
                return
            }
            generateScript(env, envConfig.host, envConfig.port)
        }
    }

    private boolean validateEnvironmentConfig(String env, def envConfig) {
        return env && envConfig?.host && envConfig?.port
    }

    private def setScriptPermissions() {
        script.sh """
            chmod +x ${SCRIPTS_DIR}/replace_remote_host_ip_*.sh
        """
    }

    private def generateScript(String env, String host, int port) {
        def scriptContent = generateScriptContent(env, host, port)
        def scriptPath = "${SCRIPTS_DIR}/replace_remote_host_ip_${env}.sh"
        script.writeFile file: scriptPath, text: scriptContent
        script.echo "✅ 已生成环境脚本: ${scriptPath}"
    }

    private def getEnvironmentConfigFiles(String env) {
        def envConfig = configurationService.getEnvironmentConfig(env)
        if (!envConfig?.config_files) {
            script.echo "⚠️ 警告：环境 ${env} 没有定义 config_files"
            return []
        }
        return envConfig.config_files
    }

    private def generateScriptContent(String env, String host, int port) {
        def upperEnv = env.toUpperCase()
        def lowerEnv = env.toLowerCase()
        def configFiles = getEnvironmentConfigFiles(env)

        // 获取特殊环境配置源目录，如果环境配置中没有定义则使用默认值
        def envConfig = configurationService.getEnvironmentConfig(env)
        def hadoopSourceDir = envConfig?.hadoop_source_dir ?: "/home/<USER>/sds/RKDT/config"
        def kerberosSourceDir = envConfig?.kerberos_source_dir ?: "/home/<USER>/sds/RKDT/config"
        
        // 构建配置文件路径映射，使用正确的 Bash 关联数组语法，并为路径添加引号
        def configPaths = configFiles.collect { config ->
            "['${config.out_path}']='${config.dst_path}'"
        }.join(" ")
        
        return """#!/bin/bash
set -e

# 定义全局变量
PACKAGE_DIR=""
INIT_MODULE="init"
ENV="${env}"
HOST="${host}"
PORT="${port}"
DEFAULT_HADOOP_SOURCE_DIR="${hadoopSourceDir}"
DEFAULT_KERBEROS_SOURCE_DIR="${kerberosSourceDir}"

# 配置文件路径映射
declare -A CONFIG_PATHS=( ${configPaths} )

# 初始化变量
HADOOP_SOURCE_DIR="\$DEFAULT_HADOOP_SOURCE_DIR"
KERBEROS_SOURCE_DIR="\$DEFAULT_KERBEROS_SOURCE_DIR"

# 解析命令行参数
while [[ \$# -gt 0 ]]; do
    case \$1 in
        --hadoop-source-dir)
            HADOOP_SOURCE_DIR="\$2"
            shift 2
            ;;
        --kerberos-source-dir)
            KERBEROS_SOURCE_DIR="\$2"
            shift 2
            ;;
        -h|--help)
            echo "使用方法: \$0 [选项] <项目根目录路径>"
            echo ""
            echo "选项:"
            echo "  --hadoop-source-dir DIR    指定Hadoop配置文件源目录 (默认: \$DEFAULT_HADOOP_SOURCE_DIR)"
            echo "  --kerberos-source-dir DIR  指定Kerberos配置文件源目录 (默认: \$DEFAULT_KERBEROS_SOURCE_DIR)"
            echo "  -h, --help                 显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  \$0 /path/to/project"
            echo "  \$0 --hadoop-source-dir /custom/hadoop/config --kerberos-source-dir /custom/kerberos/config /path/to/project"
            exit 0
            ;;
        -*)
            echo "未知选项: \$1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
        *)
            # 这是项目根目录路径
            if [ -z "\$PACKAGE_DIR" ]; then
                PACKAGE_DIR="\$1"
            else
                echo "错误: 只能指定一个项目根目录路径"
                exit 1
            fi
            shift
            ;;
    esac
done

# 检查必需参数
if [ -z "\$PACKAGE_DIR" ]; then
    echo "错误: 必须指定项目根目录路径"
    echo "使用 -h 或 --help 查看帮助信息"
    exit 1
fi

# 替换配置文件
replaceInitConfig() {
    local module="\$1"
    local current_env="\$2"
    
    echo "🔍 开始替换 \${module} 模块的配置文件..."
    
    # 源目录（当前脚本同级的环境目录）
    local script_dir=\$(dirname "\$0")

    # 遍历配置文件路径映射
    for out_path in "\${!CONFIG_PATHS[@]}"; do
        local dst_path="\${CONFIG_PATHS[\$out_path]}"
        local source_file="\${script_dir}/\${out_path}"
        local target_file="\${PACKAGE_DIR}/\${dst_path}"
        
        if [ -f "\$source_file" ]; then
            # 确保目标目录存在
            local target_dir=\$(dirname "\$target_file")
            mkdir -p "\$target_dir"
            
            echo "📝 更新配置文件: \$target_file"
            cp -f "\$source_file" "\$target_file"
            echo "✅ 配置文件更新完成: \$target_file"
        else
            echo "⚠️ 警告: 源文件不存在: \$source_file"
        fi
    done
    
    echo "✅ \${module} 模块的配置文件替换完成"
}

# 处理普通模块的函数
processNormalModule() {
    local module="\$1"
    local config_file="\$module/${CONFIG_DIR}/application-remote.properties"
    
    if [ -f "\$config_file" ]; then
        echo "📝 正在修改配置文件: \$config_file"
        sed -i "s/remote.info.host=\\\${REMOTE_INFO_HOST:.*}/remote.info.host=\\\${REMOTE_INFO_HOST:\$HOST}/g" "\$config_file"
        sed -i "s/remote.info.port=\\\${REMOTE_INFO_PORT:.*}/remote.info.port=\\\${REMOTE_INFO_PORT:\$PORT}/g" "\$config_file"
        echo "✅ 配置文件修改完成: \$config_file"
    fi
}

# 处理init模块的函数
processInitModule() {
    local module="\$1"
    local current_env="\$2"
    
    # 替换init
    replaceInitConfig "\$module" "\$current_env"
    
    # 处理ES模板
    processESTemplate "\$module"
    
    # 处理应用属性
    processAppProperties "\$module"
    
    # 处理用户数据绑定
    processUserDataBinding "\$module"
    
    # 处理Kafka配置
    processKafkaConfig "\$module"
    
    # 处理特殊环境配置
    processSpecialEnvConfig "\$module" "\$HADOOP_SOURCE_DIR" "\$KERBEROS_SOURCE_DIR"
}

# 处理ES模板的函数
processESTemplate() {
    local module="\$1"
    local template_file="\$module/${CONFIG_DIR}/everisk/transfer/es_template/bangcle_common_template.json"
    
    if [ -f "\$template_file" ]; then
        echo "📝 正在修改 ES 模板文件: \$template_file"
        sed -i 's/"number_of_replicas": [0-9]*/"number_of_replicas": 1/g' "\$template_file"
        echo "✅ ES 模板文件修改完成"
    fi
}

# 处理应用属性的函数
processAppProperties() {
    local module="\$1"
    local properties_file="\$module/${CONFIG_DIR}/application.properties"
    
    if [ -f "\$properties_file" ]; then
        echo "📝 正在修改 application.properties 文件"
        sed -i "s/^bangcle.unified.config.password-decrypt=.*\$/bangcle.unified.config.password-decrypt=true/" "\$properties_file"
        sed -i "s/^server.port=.*\$/server.port=\$PORT/" "\$properties_file"
        echo "✅ application.properties 文件修改完成"
    fi
}

# 处理用户数据绑定的函数
processUserDataBinding() {
    local module="\$1"
    local dirs=("webservice" "fdti" "relation")
    
    for dir in "\${dirs[@]}"; do
        local json_file="\$module/${CONFIG_DIR}/everisk/\$dir/binding_user_data.json"
        if [ -f "\$json_file" ]; then
            echo "📝 正在修改 \$dir 目录下的 binding_user_data.json 文件"
            sed -i 's/"key": "ud_content"/"key": "userdata"/g' "\$json_file"
            echo "✅ binding_user_data.json 文件修改完成"
        fi
    done
}

# 处理Kafka配置的函数
processKafkaConfig() {
    local module="\$1"
    
    find "\$module" -type f -name "kafka-*" | while read -r kafka_file; do
        if [ -f "\$kafka_file" ]; then
            echo "📝 正在处理 Kafka 配置文件: \$kafka_file"
            sed -i '/^[[:space:]]*#/d' "\$kafka_file"
            echo "✅ Kafka 配置文件处理完成"
        fi
    done
}

# 处理特殊环境配置的函数
processSpecialEnvConfig() {
    local module="\$1"
    local hadoop_source_dir="\$2"
    local kerberos_source_dir="\$3"
    local hadoop_dir="\$module/${CONFIG_DIR}/everisk/hadoop"
    local kerberos_dir="\$module/${CONFIG_DIR}/everisk/kerberos"
    
    # 删除旧的配置文件
    rm -f "\$hadoop_dir/core-site.xml"
    
    # 创建目录并复制新的配置文件
    mkdir -p "\$hadoop_dir"
    mkdir -p "\$kerberos_dir"
    
    # 复制Hadoop配置文件
    echo "📁 Hadoop配置源目录: \$hadoop_source_dir"
    local hadoop_files=("hbase-site.xml" "hdfs-site.xml" "core-site.xml")
    for file in "\${hadoop_files[@]}"; do
        if [ ! -f "\$hadoop_source_dir/\$file" ]; then
            echo -e "\033[31m⚠️ 警告: Hadoop配置文件不存在: \$hadoop_source_dir/\$file\033[0m"
            continue
        fi
        cp -f "\$hadoop_source_dir/\$file" "\$hadoop_dir/"
        echo "  ✅ 复制: \$file"
    done
    echo "✅ Hadoop 配置文件复制完成"
    
    # 复制Kerberos配置文件
    echo "📁 Kerberos配置源目录: \$kerberos_source_dir"
    local kerberos_files=("krb5.conf" "kafka.keytab" "hbase.keytab" "user.keytab" "bangcle_user.keytab")
    for file in "\${kerberos_files[@]}"; do
        if [ ! -f "\$kerberos_source_dir/\$file" ]; then
            echo -e "\033[31m⚠️ 警告: Kerberos配置文件不存在: \$kerberos_source_dir/\$file\033[0m"
            continue
        fi
        cp -f "\$kerberos_source_dir/\$file" "\$kerberos_dir/"
        echo "  ✅ 复制: \$file"
    done
    echo "✅ Kerberos 配置文件复制完成"
}

# 主函数
main() {
    # 检查目录是否存在
    if [ ! -d "\$PACKAGE_DIR" ]; then
        echo "❌ 错误：package目录不存在: \$PACKAGE_DIR"
        exit 1
    fi

    # 显示配置信息
    echo "📋 配置信息:"
    echo "  环境: ${upperEnv}"
    echo "  项目目录: \$PACKAGE_DIR"
    echo "  Hadoop配置源: \$HADOOP_SOURCE_DIR"
    echo "  Kerberos配置源: \$KERBEROS_SOURCE_DIR"
    echo ""
    
    echo "🔧 开始配置${upperEnv}环境..."
    
    # 遍历package目录下的所有模块
    find "\$PACKAGE_DIR" -maxdepth 1 -type d | while read -r module; do
        module_name="\$(basename "\$module")"
        if [ "\$module_name" = "\$(basename "\$PACKAGE_DIR")" ]; then
            continue
        fi
        
        if [ "\$module_name" != "\$INIT_MODULE" ]; then
            processNormalModule "\$module"
        else
            processInitModule "\$module" "${lowerEnv}"
        fi
    done
    
    # 为 web-service 目录创建 allow_cross_origin 文件
    if [ -d "\$PACKAGE_DIR/web-service" ]; then
        echo "📝 为 web-service 创建 allow_cross_origin 文件"
        touch "\$PACKAGE_DIR/web-service/allow_cross_origin"
        echo "✅ allow_cross_origin 文件创建完成"
    fi
    
    echo "✅ ${upperEnv}环境配置完成"
}

# 调用主函数
main
"""
    }
} 