{"global": {"database": {"used": {"db_password": "{{ global_database_pg_password }}"}}, "nebula": {"everisk.nebula.user": "{{ global_nebula_everisk_nebula_user }}"}}, "transfer": {"esDataAggregater.index.close.switch": "{{ esDataAggregater_index_close_switch }}"}, "switch": {"switch.main.function.banned.enabled": "{{ switch_main_function_banned_enabled }}"}, "ccb_data_push": {"ccb.logstash.config.file-path": "{{ ccb_logstash_config_file_path }}"}, "security_event": {"event.data.preparation.business.number-of-threads": "{{ event_data_preparation_business_number_of_threads }}"}, "webservice": {"server.servlet.session.timeout": "{{ webservice_server_servlet_session_timeout }}"}}