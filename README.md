# Everisk Jenkins 共享库

这是一个用于构建Everisk项目的Jenkins共享库，提供了标准化的构建流程和可复用的流水线组件。

## 目录
- [最近更新](#最近更新)
- [功能特性](#功能特性)
- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [配置指南](#配置指南)
- [故障排除](#故障排除)
- [最佳实践](#最佳实践)
- [常见问题](#常见问题)

## 最近更新

- **2024-03-21**: 优化了 ConfigService 中的 replaceInitConfig 函数，使用 environments.yaml 中定义的 out_path 作为源文件路径
- **2024-03-21**: 修改了配置文件路径映射的生成方式，使用正确的 Bash 关联数组语法
- **2024-03-21**: 为配置文件路径添加引号以处理特殊字符
- **2024-03-20**: 优化了环境配置管理，支持从环境目录直接复制配置文件
- **2024-03-20**: 添加了 iOS 配置文件的支持
- **2024-03-19**: 添加了详细的故障排除指南和最佳实践建议
- **2024-03-19**: 优化了环境配置管理，支持更灵活的配置方式
- **2024-03-19**: 增强了构建参数验证和错误处理机制
- **2023-03-12**: 修复了不支持的参数问题，移除了`gitLabConnection`参数
- **2023-03-12**: 将Multiple SCMs替换为标准的Git SCM，提高兼容性
- **2023-03-12**: 添加了Git Parameter插件的凭据配置，确保能正确获取分支和标签列表
- **2023-03-12**: 添加了常用的pipeline选项，如跳过默认检出、超时设置等
- **2023-03-12**: 优化了Git Parameter插件配置，提供更好的分支和标签选择体验
- **2023-03-12**: 增强了凭据处理，确保所有Git操作都使用正确的凭据

## 系统要求

### Jenkins 环境
- Jenkins 2.375.x 或更高版本
- 内存: 建议 4GB 以上
- 磁盘空间: 建议 50GB 以上

### 必需插件
- Pipeline (必需)
- Git (必需)
- Git Parameter (必需)
- Workspace Cleanup (必需)
- Pipeline Utility Steps (必需)
- WxWork Notification (可选，用于企业微信通知)

## 功能特性

- ✨ 统一的构建流程管理
- 🔄 多环境配置支持（PL2、VT、PROD）
- 📦 自动化打包和制品管理
- 🔔 企业微信通知集成
- 🛠️ 灵活的参数配置
- ⚙️ YAML配置文件支持
- 🔀 Git分支和标签选择支持（使用Git Parameter插件）
- 📱 iOS配置文件支持

## 代码目录结构

```
ccb-build/
├── resources/                    # 资源文件目录
│   ├── config/                  # 配置文件目录
│   │   ├── environments.yaml    # 环境配置文件
│   │   ├── others/                # 各种引用的配置文件
│   └── scripts/                # 脚本文件目录
│       └── replace_remote_host_ip_*.sh  # 环境配置脚本
├── src/                        # 源代码目录
│   └── org/                    # Java包目录
│       └── everisk/           # Everisk包
│           ├── ccb/           # CCB相关代码
│           │   └── service/   # 服务层
│           │       └── ConfigService.groovy  # 配置服务
│           └── service/       # 通用服务
│               └── ConfigurationService.groovy  # 配置管理服务
└── vars/                      # Jenkins共享库变量
    └── everiskPipeline.groovy # 主流水线定义
```

### 目录说明

- `resources/config/`: 存放所有环境相关的配置文件
  - `environments.yaml`: 环境配置主文件，包含所有环境的基本信息
  - `pl2/`, `vt/`, `prod/`: 各环境特定的配置文件目录
  - `everisk/ios/0.json`: iOS相关的配置文件

- `resources/scripts/`: 存放环境配置脚本
  - `replace_remote_host_ip_*.sh`: 用于替换各环境配置的脚本

- `src/org/everisk/`: 核心代码目录
  - `ccb/service/`: CCB特定的服务实现
  - `service/`: 通用服务实现

- `vars/`: Jenkins共享库变量定义
  - `everiskPipeline.groovy`: 主流水线定义文件

## 快速开始

### 1. 在Jenkins中配置共享库

1. 打开 Jenkins > 系统管理 > 系统配置
2. 在 "Global Pipeline Libraries" 部分点击 "添加"
3. 填写以下信息：
   - 名称：`everisk-pipeline-library`（或您喜欢的任何名称）
   - 默认版本：`main`（或您的主分支名称）
   - 检索方式：选择 "Modern SCM"
   - 选择 "Git"
   - 项目仓库：填写此共享库的 Git 仓库 URL
   - 凭据：选择有权限访问该仓库的凭据
4. 点击 "保存" 按钮

### 2. 安装必要的Jenkins插件

确保您的Jenkins安装了以下插件：

1. Pipeline
2. Git
3. Git Parameter（用于分支和标签选择）
4. Workspace Cleanup
5. Pipeline Utility Steps
6. WxWork Notification（如需企业微信通知）

安装步骤：
1. 打开 Jenkins > 系统管理 > 插件管理
2. 切换到 "可选插件" 选项卡
3. 使用搜索框查找上述插件
4. 勾选需要安装的插件，点击 "安装且不重启" 按钮
5. 安装完成后，建议重启Jenkins以确保所有插件正常工作

### 3. 配置Git凭据

这是一个**非常重要**的步骤，确保所有Git操作都能正常工作：

1. 打开 Jenkins > 系统管理 > 凭据 > 系统 > 全局凭据
2. 点击 "添加凭据"
3. 选择类型为 "用户名和密码"
4. 填写您的Git用户名和密码
5. 在 "ID" 字段中输入一个唯一标识符（例如：`junguang.chen`）
6. 点击 "确定" 保存凭据
7. 确保在 `environments.yaml` 文件中的 `credentials.git.id` 与您设置的凭据ID一致

> **重要提示**：如果您遇到 "fatal: could not read Username for 'https://...' No such device or address" 错误，这通常意味着Jenkins无法使用正确的凭据访问Git仓库。请确保您已正确配置Git凭据，并且凭据ID与配置文件中的一致。

### 4. 在您的Jenkinsfile中使用

```groovy
@Library('everisk-pipeline-library') _

everiskPipeline(
    // 构建模块配置
    defaultModules: '''
        init,
        cleaner,
        threat,
        transfer,
        analyzer-dev,
        receiver,
        threat-index,
        web-service,
        data-model-statistics,
        ccb-transfer-pbc
    '''.stripIndent().trim(),
    
    // 构建模式选项
    defaultMode: 'ccb',  // 可选值：ccb, main, hxb
    
    // Git 配置
    defaultBranch: 'rel_EVERSK_V5.1.2_ccb',
    projectRepo: 'https://gitlab.example.com/project.git',
    projectName: 'everiskServer',
    
    // 工具配置
    mavenTool: 'maven3.8.6',
    javaTool: 'jdk21'
)
```

> **提示**：所有参数都是可选的，如果不指定，将使用 `environments.yaml` 中的配置或默认值。
> 建议至少指定 `projectRepo` 和 `projectName` 这两个必要参数。

## 配置指南

所有配置都集中在 `resources/config/environments.yaml` 文件中，该文件分为三个主要部分：

### 1. 环境配置（environments）

定义不同环境（如开发、测试、生产）的连接参数：

```yaml
environments:
  pl2:  # 环境标识符
    name: PL2  # 环境名称
    host: **************  # 主机地址
    port: 8181  # 端口号
    description: "开发测试环境"  # 环境描述
    config_files:  # 需要处理的配置文件列表
      - src_path: "pl2/init/config/everisk.json"  # 源配置文件路径（可选，共享库 ）
        dst_path: "init/config/everisk.json"      # 目标配置文件路径（必需）
        out_path: "pl2/init/config/everisk.json"  # 输出文件路径（必需）
        variables_file: "pl2/init/config/variables.yaml"  # 变量文件路径（可选，共享库）
```

配置文件处理流程说明：

1. **配置文件路径说明**：
   - `src_path`: 源配置文件路径（可选），相对于 `resources/config` 目录
   - `dst_path`: 目标文件路径（必需），相对于项目的 `package` 目录
   - `out_path`: 输出文件路径（必需），相对于项目的 `package/Scripts` 目录
   - `variables_file`: 变量文件路径（可选），用于模板变量替换

2. **处理流程**：
   - 如果配置了 `src_path`：
     1. 读取源配置文件（`src_path`）
     2. 读取目标配置文件（`dst_path`）
     3. 合并配置（源配置覆盖目标配置）
     4. 如果配置了 `variables_file`，使用变量文件进行模板渲染
     5. 将结果写入输出文件（`out_path`）
   
   - 如果没有配置 `src_path`：
     1. 读取目标配置文件（`dst_path`）
     2. 如果配置了 `variables_file`，直接使用变量文件进行模板渲染
     3. 将结果写入输出文件（`out_path`）

3. **变量文件处理**：
   - 变量文件支持 YAML 或 JSON 格式
   - 如果变量文件中缺少模板中使用的变量，会抛出明确的错误提示
   - 变量文件中的变量会被用于替换配置文件中的 Jinja2 模板占位符

4. **注意事项**：
   - `dst_path` 和 `out_path` 是必需参数
   - `src_path` 和 `variables_file` 是可选参数
   - 所有路径都应使用正斜杠 `/` 作为分隔符
   - 路径中不要包含前导斜杠
   - 确保所有引用的目录都存在
   - 建议使用环境名称作为配置文件的顶级目录

### 2. 访问各种服务所需的凭据

```yaml
credentials:
  git:  # Git凭据
    id: your-git-credentials-id  # Jenkins中配置的凭据ID - 必须与Jenkins中设置的凭据ID一致
    username_var: GIT_USERNAME  # 用户名环境变量名
    password_var: GIT_PASSWORD  # 密码环境变量名
    
    # Git Parameter插件配置
    parameter:
      type: PT_BRANCH_TAG  # 参数类型：PT_BRANCH_TAG(分支和标签)、PT_BRANCH(仅分支)、PT_TAG(仅标签)
      branch_filter: origin/(.*)  # 分支过滤器，使用正则表达式
      tag_filter: '*'  # 标签过滤器，使用通配符
      sort_mode: DESCENDING_SMART  # 排序方式：ASCENDING_SMART(升序)、DESCENDING_SMART(降序)、NONE(不排序)
      list_size: '10'  # 下拉列表显示的条目数量（注意：必须是字符串类型）
```

### 3. 构建配置（build）

定义构建相关的参数：

```yaml
build:
  default_modules: "init,cleaner,threat,..."  # 默认构建模块列表
  modes:  # 可选的构建模式
    - ccb
    - main
    - hxb
  tools:  # 构建工具配置
    maven: maven3.8.6  # Maven工具版本
    java: jdk21  # JDK工具版本
```

### 4. 参数配置优先级

在 Jenkinsfile 中调用 `everiskPipeline` 时，可以通过参数覆盖默认配置。参数优先级从高到低如下：

1. Jenkinsfile 中直接传入的参数（最高优先级）
   ```groovy
   everiskPipeline(
       defaultMode: 'ccb',           // 覆盖默认构建模式
       defaultModules: 'init,threat', // 覆盖默认构建模块
       modes: ['ccb', 'main'],       // 覆盖可用构建模式列表
       defaultBranch: 'develop',     // 覆盖默认分支
       projectRepo: 'https://...',   // 覆盖项目仓库地址
       projectName: 'myProject',     // 覆盖项目名称
       mavenTool: 'maven3.8.6',      // 覆盖 Maven 工具版本
       javaTool: 'jdk21'            // 覆盖 JDK 工具版本
   )
   ```

2. `environments.yaml` 中的配置（中等优先级）
   ```yaml
   build:
     default_modules: "init,cleaner,threat,..."
     modes: ["ccb", "main", "hxb"]
     tools:
       maven: maven3.8.6
       java: jdk21
   ```

3. 代码中的硬编码默认值（最低优先级）
   ```groovy
   def defaultModules = 'init,cleaner,threat,...'
   def buildModes = ['ccb', 'main', 'hxb']
   ```
例如，对于构建模式（MODE）参数：
1. 如果在 Jenkinsfile 中指定了 `defaultMode: 'ccb'`，则使用 "ccb" 作为默认值
2. 如果没有在 Jenkinsfile 中指定，则使用 `environments.yaml` 中的配置
3. 如果 `environments.yaml` 中没有配置，则使用代码中的默认值 'ccb'

这种优先级设计允许您：
- 在 Jenkinsfile 中灵活覆盖默认配置
- 在 `environments.yaml` 中集中管理环境配置
- 在代码中提供合理的默认值作为后备方案

## 配置修改指南

### 修改环境配置

如需添加或修改环境配置，编辑 `environments` 部分：

```yaml
environments:
  # 添加新环境
  dev:
    name: DEV
    host: **************
    port: 8181
    description: "开发环境"
    config_files:
      - src_path: "dev/init/config/everisk.json"
        dst_path: "init/config/everisk.json"
        out_path: "dev/init/config/everisk.json"
```

配置文件路径说明：
- `src_path`: 源配置文件路径，相对于 `resources/config` 目录
- `dst_path`: 目标文件路径，相对于项目的 `package` 目录
- `out_path`: 输出文件路径，相对于项目的 `package/Scripts` 目录

注意事项：
1. 所有路径都应使用正斜杠 `/` 作为分隔符
2. 路径中不要包含前导斜杠
3. 确保所有引用的目录都存在
4. 建议使用环境名称作为配置文件的顶级目录

### 修改Git参数配置

如需调整Git分支和标签选择的行为，编辑 `credentials.git.parameter` 部分：

```yaml
parameter:
  # 仅显示分支
  type: PT_BRANCH
  # 仅显示以"release/"开头的分支
  branch_filter: origin/release/(.*)
  # 增加显示条目数量（必须是字符串类型）
  list_size: '20'
```

### 修改构建模式

如需添加或修改构建模式，编辑 `build.modes` 部分：

```yaml
modes:
  - ccb
  - main
  - hxb
  - custom  # 添加新的构建模式
```

## 分支和标签选择

流水线使用Git Parameter插件提供分支和标签选择功能：

1. 当您运行流水线时，Jenkins会显示一个构建参数页面
2. 在"GIT_REF"参数中，您可以从下拉列表中选择要构建的分支或标签
3. 列表会自动显示仓库中的所有分支和标签（根据配置的过滤器）
4. 您可以使用快速过滤器搜索特定的分支或标签
5. 选择后点击"开始构建"按钮开始构建

### Git Parameter插件凭据配置

Git Parameter插件需要凭据才能访问Git仓库获取分支和标签列表：

1. 在Jenkins中配置用户名和密码类型的凭据
2. 记录凭据ID（例如：`junguang.chen`）
3. 在配置文件中设置 `credentials.git.id` 为该凭据ID
4. 流水线会自动将该凭据ID传递给Git Parameter插件的 `credentialsId` 参数

```groovy
[$class: 'GitParameterDefinition',
    // 其他参数...
    useRepository: config.projectRepo,
    credentialsId: gitCredentials.id  // 使用配置文件中的凭据ID
]
```

### Git Parameter插件支持的参数

Git Parameter插件支持以下参数：

- `name`: 参数名称（必需）
- `type`: 参数类型，如`PT_BRANCH_TAG`、`PT_BRANCH`、`PT_TAG`等（必需）
- `defaultValue`: 默认值
- `description`: 参数描述
- `branch`: 分支名称（通常留空）
- `branchFilter`: 分支过滤器，使用正则表达式
- `tagFilter`: 标签过滤器，使用通配符
- `sortMode`: 排序方式，如`DESCENDING_SMART`
- `selectedValue`: 选择值的处理方式，如`DEFAULT`
- `quickFilterEnabled`: 是否启用快速过滤器
- `listSize`: 下拉列表显示的条目数量（必须是字符串类型）
- `useRepository`: 要使用的Git仓库URL
- `credentialsId`: 访问Git仓库的凭据ID

**注意**：某些参数可能在不同版本的插件中不受支持，如果遇到警告或错误，请检查您的插件版本并相应调整参数。

### 自定义分支和标签过滤器

您可以通过修改配置文件中的 `branch_filter` 和 `tag_filter` 来控制显示哪些分支和标签：

- **仅显示特定前缀的分支**：`branch_filter: origin/feature/(.*)`
- **仅显示版本标签**：`tag_filter: v[0-9]*`
- **排除某些分支**：`branch_filter: ^(?!origin/obsolete/).*`

## 源码管理

流水线使用标准的Git SCM插件来管理源码检出：

1. 在流水线的`Checkout`阶段配置了Git SCM
2. 使用`credentialsId`参数指定访问Git仓库的凭据
3. 使用`branches`参数指定要检出的分支或标签
4. 使用`extensions`参数配置检出选项，如目标目录

```groovy
checkout([
    $class: 'GitSCM',
    branches: [[name: "${params.GIT_REF}"]],
    userRemoteConfigs: [[
        credentialsId: "${gitCredentials.id}",
        url: "${config.projectRepo}"
    ]],
    extensions: [
        [$class: 'RelativeTargetDirectory', relativeTargetDir: "${config.projectName}"]
    ]
])
```

## 构建流程

1. 初始化服务和配置
2. 清理工作空间
3. 检出代码（使用Git SCM）
4. 编译构建
5. 生成环境配置脚本
6. 打包
7. 通知（成功/失败）

## 最佳实践

### 1. 分支管理
```groovy
everiskPipeline(
    defaultBranch: 'main',
    branchPattern: '^(feature|hotfix|release)/',
    protectedBranches: ['main', 'develop']
)
```

### 2. 环境配置模板
```yaml
environments:
  dev:
    name: "开发环境"
    host: "${DEV_HOST}"
    port: "${DEV_PORT}"
    params:
      MAX_MEMORY: "4g"
      DEBUG_MODE: "true"
  
  prod:
    name: "生产环境"
    host: "${PROD_HOST}"
    port: "${PROD_PORT}"
    params:
      MAX_MEMORY: "8g"
      DEBUG_MODE: "false"
```

### 3. 构建优化建议
- 使用并行构建加速多模块项目
- 配置合适的超时时间
- 实现增量构建
- 合理使用缓存机制

## 故障排除

### 构建失败检查清单
1. 检查Git凭据配置
2. 验证环境变量设置
3. 确认Jenkins权限设置
4. 检查网络连接状态
5. 查看详细构建日志

### 常见错误码解释
- E001: Git凭据无效
- E002: 环境配置缺失
- E003: 构建超时
- E004: 依赖下载失败

## 常见问题

### 1. 分支/标签列表为空

**问题**：运行流水线时，Git Parameter插件没有显示任何分支或标签。

**解决方案**：
- 检查Git凭据是否正确配置且有权限访问仓库
- 确认 `branch_filter` 和 `tag_filter` 配置正确
- 确保已正确配置Multiple SCMs源码管理
- 尝试将 `type` 设置为 `PT_BRANCH` 或 `PT_TAG` 单独测试
- 检查Jenkins日志中是否有相关错误信息

### 2. Git凭据错误

**问题**：遇到 "fatal: could not read Username for 'https://...' No such device or address" 错误。

**解决方案**：
- 确保已在Jenkins中正确配置Git凭据
- 确保凭据ID与配置文件中的 `credentials.git.id` 一致
- 检查Jenkins日志中是否有凭据相关的错误信息
- 尝试在Jenkins中手动测试凭据是否有效

### 3. 构建失败

**问题**：构建过程中出现错误。

**解决方案**：
- 检查Git凭据是否正确
- 确认环境变量配置
- 查看Jenkins日志详细信息
- 确保所选分支/标签存在且可访问

### 4. 环境配置问题

**问题**：环境配置不正确或不生效。

**解决方案**：
- 检查 `environments.yaml` 文件中的配置是否正确
- 确保配置文件格式符合YAML规范
- 检查配置文件的权限设置

### 5. Git Parameter插件问题

**问题**：Git Parameter插件不工作或行为异常。

**解决方案**：
- 如果分支/标签列表为空，检查Git凭据是否有权限访问仓库
- 确认 `credentialsId` 参数是否正确设置，且对应的凭据在Jenkins中存在
- 如果加载时间过长，可以调整 `branch_filter` 和 `tag_filter` 来限制结果数量
- 如果需要刷新列表，可以在Jenkins任务页面点击"立即构建"按钮旁边的下拉箭头，选择"刷新参数"
- 尝试使用完整的类定义 `[$class: 'GitParameterDefinition', ...]` 而不是简写形式
- 确保所有参数类型正确，特别是 `list_size` 必须是字符串类型
- 如果您的Git Parameter插件版本较旧，可能不支持某些参数，请考虑升级插件
- 如果遇到"Unknown parameter(s) found"警告，请移除不支持的参数

### 6. 不支持的参数问题

**问题**：遇到"Unknown parameter(s) found"警告或错误。

**解决方案**：
- 检查警告信息中提到的不支持的参数，并将其从配置中移除
- 不同版本的插件支持的参数可能不同，请参考插件文档
- 对于Git Parameter插件，常见的不支持参数包括`gitLabConnection`等
- 如果需要使用特定功能，请考虑升级插件到支持该功能的版本 
