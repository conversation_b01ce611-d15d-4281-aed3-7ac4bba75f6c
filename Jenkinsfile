@Library('everisk-pipeline-library@dev') _

// 使用共享库中的流水线
everiskPipeline(
    // 构建模块配置
    defaultModules: '''
        init,
        cleaner,
        threat,
        transfer,
        analyzer-dev,
        receiver,
        threat-index,
        web-service,
        data-model-statistics,
        security-event, 
        ccb-logstash-config-file-generate
    '''.stripIndent().trim(),
    
    // 构建模式选项
    // modes: ['ccb', 'main', 'hxb'],
    defaultMode: 'ccb',
    
    // Git配置
    defaultBranch: 'rel_EVERSK_V5.1.2_ccb',
    projectRepo: 'https://gitlab.bangcle.com/bangcle/everiskServer.git',
    projectName: 'everiskServer',
    
    // 工具配置
    mavenTool: 'maven3.8.6',
    javaTool: 'jdk21'
)

// 如果需要在某些情况下动态修改环境配置，可以这样做：
// def config = new org.everisk.service.ConfigurationService(this)
// 
// // 例如：在特定分支上使用不同的环境配置
// if (env.BRANCH_NAME == 'develop') {
//     config.updateEnvironmentConfig('pl2', [
//         host: '**************',
//         port: 8182
//     ])
// }
//
// // 或者根据参数选择不同的配置
// if (params.CUSTOM_ENV == 'test') {
//     config.updateEnvironmentConfig('vt', [
//         host: '**************',
//         port: 8282
//     ])
// } 
  