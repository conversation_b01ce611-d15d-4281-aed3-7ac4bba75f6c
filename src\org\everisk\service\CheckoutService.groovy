package org.everisk.service

class CheckoutService implements Serializable {
    def script

    CheckoutService(script) {
        this.script = script
    }

    def doCheckout(Map config) {
        // 初始化配置服务并加载配置
        def configurationService = new ConfigurationService(script)
        configurationService.loadConfig()
        
        // 获取Git凭据ID
        def gitCredentialsId = configurationService.getCredentials().git.id
        
        // git-parameter插件返回的是纯分支名或标签名，不需要解析前缀
        def refValue = config.ref
        
        script.echo "使用凭据ID: ${gitCredentialsId} 检出代码"
        
        // 使用withCredentials包装所有Git操作
        script.withCredentials([script.usernamePassword(
            credentialsId: gitCredentialsId,
            usernameVariable: 'GIT_USERNAME',
            passwordVariable: 'GIT_PASSWORD'
        )]) {
            // 判断是分支还是标签
            def isBranch = true
            def branchOutput = script.sh(
                script: """
                    git ls-remote --heads ${config.repoUrl} | grep -q "refs/heads/${refValue}" || true
                """,
                returnStatus: true
            )
            
            if (branchOutput != 0) {
                isBranch = false
            }
            
            def refType = isBranch ? "分支" : "标签"
            
            script.echo "检出${refType}: ${refValue} 到项目目录: ${config.projectName}"
            
            // 使用Git凭据检出代码
            script.checkout([
                $class: 'GitSCM',
                branches: [[name: refValue]],
                userRemoteConfigs: [[
                    credentialsId: gitCredentialsId,
                    url: config.repoUrl
                ]],
                extensions: [
                    [$class: 'RelativeTargetDirectory', relativeTargetDir: config.projectName]
                ]
            ])
            
            // 获取当前检出的引用
            def gitRef = script.sh(
                script: "cd ${config.projectName} && git rev-parse --abbrev-ref HEAD",
                returnStdout: true
            ).trim()
            
            script.echo "已成功检出 ${config.projectName} 项目的${refType}: ${gitRef}"
        }
    }
} 