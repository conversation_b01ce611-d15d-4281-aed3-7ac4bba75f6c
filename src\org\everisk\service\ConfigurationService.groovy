package org.everisk.service

@Grab('org.yaml:snakeyaml:1.29')
import org.yaml.snakeyaml.Yaml

class ConfigurationService implements Serializable {
    def script
    def config

    ConfigurationService(script) {
        this.script = script
    }

    def loadConfig() {
        def configFile = script.libraryResource 'config/environments.yaml'
        config = new Yaml().load(configFile)
    }

    def getEnvironmentConfig(String env) {
        if (!config) {
            loadConfig()
        }
        return config.environments[env]
    }

    def getCredentials() {
        if (!config) {
            loadConfig()
        }
        return config.credentials
    }

    def getBuildConfig() {
        if (!config) {
            loadConfig()
        }
        return config.build
    }

    def getAllEnvironments() {
        if (!config) {
            loadConfig()
        }
        return config.environments
    }

    def updateEnvironmentConfig(String env, Map updates) {
        if (!config) {
            loadConfig()
        }
        
        if (!config.environments[env]) {
            throw new IllegalArgumentException("Environment ${env} not found")
        }
        
        config.environments[env] = config.environments[env] + updates
        saveConfig()
    }

    private void saveConfig() {
        def yaml = new Yaml()
        def updatedConfig = yaml.dump(config)
        
        // 保存到工作空间的临时配置文件
        script.writeFile file: '.jenkins-config.yaml', text: updatedConfig
        
        script.echo "配置已更新，请注意：这些更改是临时的。如需永久保存，请更新代码仓库中的配置文件。"
    }
}