package org.everisk.service

class BuildService implements Serializable {
    def script

    BuildService(script) {
        this.script = script
    }

    def doBuild(Map config) {
        script.dir(config.projectName) {
            script.withEnv([
                "JAVA_HOME=${config.javaHome}",
                "MAVEN_HOME=${config.mavenHome}",
                "PATH=${config.javaHome}/bin:${config.mavenHome}/bin:${script.env.PATH}"
            ]) {
                script.sh """#!/bin/bash
                    set -eo pipefail
                    
                    echo "=== 环境变量验证 ==="
                    echo "JAVA_HOME: ${config.javaHome}"
                    echo "MAVEN_HOME: ${config.mavenHome}"
                    echo "PATH: \$PATH"
                    echo "\$PWD"
                    
                    # 权限设置
                    if [ ! -x "shell/makeByTag.sh" ]; then
                        chmod +x "shell/makeByTag.sh"
                    fi
                    
                    # 带参数校验的执行
                    if [ -z "${config.modules}" ]; then
                        echo "❌ 错误：MODULES参数不能为空"
                        exit 1
                    fi
                    
                    echo "🚀 开始构建..."
                    "shell/makeByTag.sh" \\
                        "${config.modules}" \\
                        ${config.mode}
                    
                    echo "✅ 构建完成"
                """
            }
        }
    }
} 