#!/usr/bin/env groovy

import org.everisk.service.*
import org.everisk.ccb.service.ConfigService

def call(Map config = [:]) {
    // 声明服务变量
    def checkoutService
    def buildService
    def configService
    def packageService
    def notificationService
    def configMergeService
    def configurationService = new ConfigurationService(this)
    
    // 加载配置
    configurationService.loadConfig()
    
    // 获取Git凭据配置
    def gitCredentials = configurationService.getCredentials()?.git ?: [id: 'junguang.chen']
    def gitParam = gitCredentials.parameter ?: [
        type: 'PT_BRANCH_TAG',
        branch_filter: 'origin/(.*)',
        tag_filter: '*',
        sort_mode: 'DESCENDING_SMART',
        list_size: '10'  // 修改为字符串类型
    ]

    // 获取构建配置
    def buildConfig = configurationService.getBuildConfig()
    def buildModules = buildConfig?.modules ?: 'init,cleaner,threat,transfer,analyzer-dev,receiver,threat-index,web-service,data-model-statistics,ccb-transfer-pbc'
    def buildModes = buildConfig?.modes ?: ['ccb', 'main', 'hxb', 'cebbank']

    // 在 pipeline 外部定义参数，使用 properties 方法
    properties([
        buildDiscarder(
            logRotator(
                artifactDaysToKeepStr: '3',  // 保留构建产物的天数
                artifactNumToKeepStr: '3',  // 保留构建产物的数量
                daysToKeepStr: '1',         // 保留构建记录的天数
                numToKeepStr: '2'           // 保留构建记录的数量
            )
        ),
        disableConcurrentBuilds(),  // 禁止并发构建
        gitLabConnection(
            gitLabConnection: 'junguang.token',  // 引用 GitLab Connection 名称
            jobCredentialId: ''  // 如果需要凭据 ID，请填写；否则留空
        ),
        parameters([
            string(name: 'MODULES', defaultValue: config.defaultModules ?: buildModules, description: '要打包的模块名称, all表示所有模块, 多个模块使用逗号分隔'),
            choice(name: 'MODE', choices: config.modes ?: buildModes, defaultValue: config.defaultMode ?: 'ccb', description: '打包的模式'),
            gitParameter(
                name: 'GIT_REF',
                type: gitParam.type,
                branch: '',
                branchFilter: gitParam.branch_filter,
                defaultValue: config.defaultBranch ?: 'master',
                description: '要检出的Git分支或标签',
                listSize: gitParam.list_size,
                quickFilterEnabled: true,
                selectedValue: 'DEFAULT',
                sortMode: gitParam.sort_mode,
                tagFilter: gitParam.tag_filter,
                useRepository: config.projectRepo ?: 'https://gitlab.bangcle.com/bangcle/everiskServer.git'
            )
        ])
    ])
            



    pipeline {
        agent any

        environment {
            PROJECT_REPO = "${config.projectRepo ?: 'https://gitlab.bangcle.com/bangcle/everiskServer.git'}"
            PROJECT_NAME = "${config.projectName ?: 'everiskServer'}"
            MAVEN_HOME = tool(name: "${config.mavenTool ?: 'maven3.8.6'}", type: 'hudson.tasks.Maven$MavenInstallation')
            JAVA_HOME = tool(name: "${config.javaTool ?: 'jdk21'}", type: 'hudson.model.JDK')
            GIT_CREDENTIALS_ID = "${gitCredentials.id}"
        }
        
        options {
            // 常用的pipeline选项
            skipDefaultCheckout(true) // 跳过默认的checkout，我们将使用自定义的checkout
            timestamps() // 在日志中显示时间戳
            timeout(time: 60, unit: 'MINUTES') // 设置超时时间
            disableConcurrentBuilds() // 禁止并发构建
        }
        
        stages {
            
            stage('初始化') {
                steps {
                    script {
                        // 初始化所有服务
                        checkoutService = new CheckoutService(this)
                        buildService = new BuildService(this)
                        configService = new ConfigService(this)
                        packageService = new PackageService(this)
                        notificationService = new NotificationService(this)
                        configMergeService = new ConfigMergeService(this)
                        
                        echo "选择的Git引用: ${params.GIT_REF}"
                        echo "使用的Git凭据ID: ${gitCredentials.id}"
                        echo "使用的分支过滤器: ${gitParam.branch_filter}"
                    }
                }
            }

            stage('清理') {
                steps {
                    cleanWs()
                }
            }

            stage('Checkout') {
                steps {
                    script {
                        // 使用标准的Git SCM进行检出
                        checkout([
                            $class: 'GitSCM',
                            branches: [[name: "${params.GIT_REF}"]],
                            userRemoteConfigs: [[
                                credentialsId: "${gitCredentials.id}",
                                url: "${config.projectRepo ?: 'https://gitlab.bangcle.com/bangcle/everiskServer.git'}"
                            ]],
                            extensions: [
                                [$class: 'RelativeTargetDirectory', relativeTargetDir: "${config.projectName ?: 'everiskServer'}"]
                            ]
                        ])
                        
                        echo "检出代码，分支/标签: ${params.GIT_REF}"
                    }
                }
            }

            stage('Build') {
                steps {
                    script {
                        buildService.doBuild(
                            projectName: env.PROJECT_NAME,
                            javaHome: env.JAVA_HOME,
                            mavenHome: env.MAVEN_HOME,
                            modules: params.MODULES,
                            mode: params.MODE
                        )
                    }
                }
            }

            // 新增多环境配置合并阶段
            stage('处理对应环境everisk.json') {
                steps {
                    script {
                        configMergeService.mergeAllEnvironments(env.PROJECT_NAME)
                    }
                }
            }


            stage('生成环境配置脚本') {
                steps {
                    script {
                        configService.generateEnvironmentScripts(
                            projectName: env.PROJECT_NAME
                        )
                    }
                }
            }

            stage('Package') {
                steps {
                    script {
                        packageService.createPackage(
                            projectName: env.PROJECT_NAME,
                            branch: params.GIT_REF
                        )
                    }
                }
            }
        }

        post {
            success {
                script {
                    notificationService?.sendSuccess(
                        artifactsUrl: env.RUN_ARTIFACTS_DISPLAY_URL,
                        buildUrl: env.BUILD_URL
                    )
                }
            }
            failure {
                script {
                    notificationService?.sendFailure(
                        buildUrl: env.BUILD_URL,
                        failureStage: env.FAILURE_STAGE
                    )
                }
            }
        }
    }
}
